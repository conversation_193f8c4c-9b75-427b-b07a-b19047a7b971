import { getStateManager } from '../utils/stateManager.js'
import { getConfigManager } from '../utils/configManager.js'

// TabBar 基础配置
const BASE_TABBAR_CONFIGS = {
  '1': [ // 业主基础配置
    {
      pagePath: "/pages/index/index",
      text: "首页",
      icon: "wap-home-o",
      required: true // 必需项
    }, {
      pagePath: "/pages/service/index",
      text: "信息公开",
      icon: "bullhorn-o",
      required: true // 必需项
    }, {
      pagePath: "/pages/markicam/index",
      text: "工作照片",
      icon: "photo-o",
      configKey: "enable_markicam" // 配置控制项
    }, {
      pagePath: "/pages/finance/finance",
      text: "收支公示",
      icon: "balance-list-o",
      configKey: "enable_financial" // 配置控制项
    }, {
      pagePath: "/pages/mine/index",
      text: "我的",
      icon: "user-o",
      required: true // 必需项
    }
  ],
  '2': [ // 物业基础配置
    {
      pagePath: "/pages/property/dashboard/index",
      text: "工作台",
      icon: "wap-home-o",
      required: true // 必需项
    }, {
      pagePath: "/pages/property/orders/index",
      text: "工单",
      icon: "orders-o",
      required: true // 必需项
    }, {
      pagePath: "/pages/property/patrol/index",
      text: "巡检",
      icon: "records-o",
      required: true // 必需项
    }, {
      pagePath: "/pages/markicam/index",
      text: "工作照片",
      icon: "photo-o",
      configKey: "enable_markicam" // 配置控制项
    }, {
      pagePath: "/pages/property/profile/index",
      text: "我的",
      icon: "user-o",
      required: true // 必需项
    }
  ]
}

Component({
  data: {
    selected: 0,
    list: [], // 初始为空，在initTabBar中设置
    currentUserType: '1' // 缓存当前用户类型
  },

  lifetimes: {
    attached() {
      this.initTabBar()
      this.setupConfigListener()
    },

    detached() {
      this.removeConfigListener()
    }
  },

  methods: {
    onChange(event) {
      const index = event.detail
      const list = this.data.list || []
      const item = list[index]
      if (!item || !item.pagePath) {
        return
      }
      wx.switchTab({ url: item.pagePath })
    },

    /**
     * 初始化TabBar（只在组件创建时调用一次）
     */
    initTabBar() {
      try {
        const stateManager = getStateManager()
        const state = stateManager.getState()
        const inferredUserType = this.determineInitialUserType(state.userType, state.isPropertyUser)

        this.updateTabBarConfig(inferredUserType)
        // 同步选中态
        this.syncSelectedByRoute()
      } catch (error) {
        // 如果初始化失败，使用默认配置
        this.setData({
          list: this.getDefaultTabBarList('1'),
          currentUserType: '1'
        })
        this.syncSelectedByRoute()
      }
    },

    /**
     * 更新TabBar配置（支持动态配置）
     */
    updateTabBarConfig(userType) {
      // 统一解析用户类型，避免因状态未就绪而回退到业主菜单
      const effectiveType = (userType === '1' || userType === '2' || userType === 1 || userType === 2)
        ? String(userType)
        : (this.data.currentUserType === '1' || this.data.currentUserType === '2')
          ? this.data.currentUserType
          : this.resolveUserType()

      const newList = this.buildDynamicTabBarList(effectiveType)

      this.setData({
        list: newList,
        currentUserType: effectiveType
      })
    },

    /**
     * 根据页面路径查找在当前tabBar中的索引
     */
    getIndexByPath(path) {
      return this.data.list.findIndex(item => item.pagePath === path)
    },

    /**
     * 根据配置动态构建TabBar列表
     */
    buildDynamicTabBarList(userType) {
      try {
        const configManager = getConfigManager()
        const baseConfig = BASE_TABBAR_CONFIGS[userType] || BASE_TABBAR_CONFIGS['1']

        // 检查配置管理器是否已完全初始化
        const configStatus = configManager.getStatus()
        if (!configStatus.isInitialized) {
          console.log('[TabBar] 配置管理器未完全初始化，使用当前TabBar配置')
          // 如果配置未初始化且当前已有TabBar列表，保持不变
          if (this.data.list && this.data.list.length > 0) {
            return this.data.list
          }
          // 否则使用默认配置
          return this.getDefaultTabBarList(userType)
        }

        // 过滤配置项，只保留启用的功能
        const filteredList = baseConfig.filter(item => {
          if (item.required) return true
          if (item.configKey) {
            const isEnabled = configManager.isEnabled(item.configKey)
            console.log(`[TabBar] 配置检查 ${item.configKey}: ${isEnabled}`)
            return isEnabled
          }
          return true
        })

        console.log(`[TabBar] 构建TabBar列表，用户类型: ${userType}, 菜单数量: ${filteredList.length}`)

        // 移除配置相关的临时字段，只保留TabBar需要的字段
        return filteredList.map(item => ({
          pagePath: item.pagePath,
          text: item.text,
          icon: item.icon
        }))
      } catch (error) {
        console.error('[TabBar] 构建TabBar列表失败:', error)
        // 降级到默认配置
        return this.getDefaultTabBarList(userType)
      }
    },

    /**
     * 获取默认TabBar列表（降级方案）
     */
    getDefaultTabBarList(userType) {
      const defaultConfigs = {
        '1': [
          { pagePath: "/pages/index/index", text: "首页", icon: "wap-home-o" },
          { pagePath: "/pages/service/index", text: "信息公开", icon: "bullhorn-o" },
          { pagePath: "/pages/finance/finance", text: "收支公示", icon: "balance-list-o" },
          { pagePath: "/pages/mine/index", text: "我的", icon: "user-o" }
        ],
        '2': [
          { pagePath: "/pages/property/dashboard/index", text: "工作台", icon: "dashboard-o" },
          { pagePath: "/pages/property/orders/index", text: "工单", icon: "orders-o" },
          { pagePath: "/pages/property/patrol/index", text: "巡检", icon: "check-o" },
          { pagePath: "/pages/property/profile/index", text: "我的", icon: "user-o" }
        ]
      }

      return defaultConfigs[userType] || defaultConfigs['1']
    },    /**
     * 根据路由同步选中项
     */
    syncSelectedByRoute(retry = 0) {
      try {
        const pages = typeof getCurrentPages === 'function' ? getCurrentPages() : []
        const hasPages = Array.isArray(pages) && pages.length > 0
        if (!hasPages) {
          if (retry < 3) {
            setTimeout(() => this.syncSelectedByRoute(retry + 1), 50)
          }
          return
        }
        const current = pages[pages.length - 1]
        const routeVal = current && (current.route || current.__route__)
        if (!routeVal) {
          return
        }
        const route = '/' + routeVal
        const list = this.data.list || []
        const index = list.findIndex(item => item && item.pagePath === route)
        if (index >= 0) {
          this.setData({ selected: index })
        }
      } catch (e) {
      }
    },

    /**
     * 推断初始用户类型
     */
    determineInitialUserType(userType, isPropertyUser) {
      // 如果明确是物业用户，优先返回'2'
      if (isPropertyUser === true) return '2'
      // 其次使用state中的userType
      if (userType === '2' || userType === 2) return '2'
      return '1'
    },

    /**
     * 解析/回退用户类型，确保始终为 '1' 或 '2'
     */
    resolveUserType(fallback) {
      try {
        const stateManager = getStateManager()
        const state = stateManager.getState()
        // 优先 isPropertyUser，再其次 userType，最后 fallback
        if (state && state.isPropertyUser === true) return '2'
        if (state && (state.userType === '2' || state.userType === 2)) return '2'
        if (fallback === '2' || fallback === 2) return '2'
        return '1'
      } catch (e) {
        return (fallback === '2' || fallback === 2) ? '2' : '1'
      }
    },

    /**
     * 根据用户类型更新TabBar配置（兼容旧接口）
     */
    updateTabBarByUserType() {
      try {
        const stateManager = getStateManager()
        const state = stateManager.getState()
        const userType = this.determineInitialUserType(state.userType, state.isPropertyUser)

        this.updateTabBarConfig(userType)
      } catch (error) {
      }
    },

    /**
     * 设置当前选中的tab
     */
    setSelected(index) {
      this.setData({
        selected: index
      })
    },

    /**
     * 设置配置监听器
     */
    setupConfigListener() {
      try {
        const configManager = getConfigManager()
        configManager.addListener('tabbar-config', (event) => {
          console.log('TabBar收到配置变更通知:', event.type)
          // 延迟刷新TabBar，确保配置完全加载
          if (this.configRefreshTimer) {
            clearTimeout(this.configRefreshTimer)
          }
          this.configRefreshTimer = setTimeout(() => {
            console.log('TabBar延迟刷新配置')
            this.refreshTabBarConfig()
          }, 100) // 延迟100ms
        })
      } catch (error) {
        console.error('设置配置监听器失败:', error)
      }
    },

    /**
     * 移除配置监听器
     */
    removeConfigListener() {
      try {
        const configManager = getConfigManager()
        configManager.removeListener('tabbar-config')

        // 清理定时器
        if (this.configRefreshTimer) {
          clearTimeout(this.configRefreshTimer)
          this.configRefreshTimer = null
        }
      } catch (error) {
        console.error('移除配置监听器失败:', error)
      }
    },

    /**
     * 刷新TabBar配置（强制更新）
     */
    refreshTabBarConfig() {
      try {
        // 使用当前组件内的用户类型为主，避免配置刷新时误回退为业主菜单
        const effectiveType = this.resolveUserType(this.data.currentUserType)

        // 强制重新构建TabBar
        const newList = this.buildDynamicTabBarList(effectiveType)

        this.setData({
          list: newList,
          currentUserType: effectiveType
        })

        // 配置刷新后，同步选中项，避免“未选中高亮”问题
        this.syncSelectedByRoute()
      } catch (error) {
      }
    }
  }
})