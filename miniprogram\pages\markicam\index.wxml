<view class="container">
  <!-- 顶部筛选条件 -->
  <view class="filters">
    <van-dropdown-menu>
      <van-dropdown-item value="{{filters.moment_type}}" options="{{typeOptions}}" bind:change="onTypeFilterChange" />
      <van-dropdown-item id="timeRangeItem" title="{{timeRangeLabel}}" bind:open="onTimeItemOpen">
        <view class="time-quick">
          <view class="quick-btn" data-range="today" bindtap="onQuickRange">今日</view>
          <view class="quick-btn" data-range="yesterday" bindtap="onQuickRange">昨日</view>
          <view class="quick-btn" data-range="thisMonth" bindtap="onQuickRange">本月</view>
          <view class="quick-btn" data-range="lastMonth" bindtap="onQuickRange">上月</view>
          <view class="quick-btn" data-range="last7" bindtap="onQuickRange">近7天</view>
          <view class="quick-btn" data-range="custom" bindtap="onQuickRange">自定义</view>
        </view>
        <view wx:if="{{showDatePicker}}" class="date-picker">
          <van-datetime-picker type="date" value="{{customDate}}" bind:input="onDatePick" bind:confirm="onDateConfirm" bind:cancel="onDateCancel" />
        </view>
      </van-dropdown-item>
    </van-dropdown-menu>
  </view>

  <!-- 动态列表 -->
  <view class="moments-list" wx:if="{{momentsList.length > 0}}">
    <view class="moment-card" 
          wx:for="{{momentsList}}" 
          wx:key="id" 
          wx:for-index="index"
          wx:for-item="moment">
      
      <!-- 用户信息头部 -->
      <view class="moment-header">
        <view class="user-info">
          <view class="avatar-icon">
            <van-icon name="friends-o" size="36rpx" color="#1989fa" />
          </view>
          <view class="user-details">
            <text class="username">{{moment.displayName}}</text>
            <text class="team-name" wx:if="{{moment.team_name}}">{{moment.team_name}}</text>
          </view>
        </view>
        <text class="post-time">{{moment.post_time_str}}</text>
      </view>

      <!-- 水印内容 -->
      <view class="moment-content" wx:if="{{moment.parsedContent || moment.content}}">
        <block wx:if="{{moment.parsedContent && moment.parsedContent.length}}">
          <view class="content-row" wx:for="{{moment.parsedContent}}" wx:key="index">
            <text class="content-label">{{item.label}}：</text>
            <text class="content-value">{{item.value}}</text>
          </view>
        </block>
        <block wx:else>
          <text class="content-text">{{moment.content}}</text>
        </block>
      </view>

      <!-- 媒体内容 -->
      <view class="moment-media" wx:if="{{moment.url}}">
        <!-- 图片 -->
        <image wx:if="{{moment.isImage}}"
               class="moment-image"
               src="{{moment.url}}"
               mode="aspectFill"
               bindtap="previewImage"
               data-url="{{moment.url}}" />
        
        <!-- 视频 -->
        <video wx:if="{{moment.isVideo}}" 
               class="moment-video" 
               src="{{moment.url}}"
               poster="{{moment.url}}"
               bindtap="playVideo"
               data-url="{{moment.url}}" />
      </view>

      <!-- 地理位置信息 -->
      <view class="location-info" wx:if="{{moment.lng && moment.lat}}" bindtap="onLocationTap" data-type="{{moment.moment_type}}">
        <van-icon name="location-o" size="24rpx" color="#999" />
        <text class="location-text">{{moment.mark_name || '位置信息'}}</text>
      </view>

      <!-- 互动按钮 -->
      <view class="moment-actions">
        <view class="action-item">
          <van-icon name="eye-o" size="32rpx" color="#666" />
          <text class="action-text">{{moment.view_count || 1}}</text>
        </view>
        
        <!-- 分享放中间，使用 open-type=share -->
        <button class="action-item share-btn" open-type="share" data-index="{{index}}">
          <van-icon name="share" size="32rpx" color="#666" />
          <text class="action-text">分享</text>
        </button>

        <!-- 点赞放右侧，换为good-job图标 -->
        <view class="action-item"
              bindtap="toggleLike"
              data-index="{{index}}">
          <van-icon name="{{moment.isLiked ? 'good-job' : 'good-job-o'}}"
                    size="32rpx"
                    color="{{moment.isLiked ? '#ff4757' : '#666'}}" />
          <text class="action-text {{moment.isLiked ? 'liked' : ''}}">{{moment.likeCount || '点赞'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{momentsList.length > 0}}">
    <view class="load-more-btn" wx:if="{{hasMore && !loading}}" bindtap="loadMoreData">
      <text class="load-more-text">加载更多</text>
    </view>
    <view class="loading-text" wx:if="{{loading}}">
      <van-loading size="20rpx" color="#1890ff" />
      <text class="loading-label">加载中...</text>
    </view>
    <view class="no-more-text" wx:if="{{!hasMore && !loading}}">
      <text>没有更多动态了</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{momentsList.length === 0 && !loading}}">
    <van-icon name="photo-o" size="120rpx" color="#ddd" />
    <text class="empty-text">暂时没有动态内容~</text>
  </view>

  <!-- 首次加载状态 -->
  <view class="initial-loading" wx:if="{{loading && momentsList.length === 0}}">
    <van-loading size="40rpx" color="#1890ff" />
    <text class="loading-text">加载中...</text>
  </view>
</view>
